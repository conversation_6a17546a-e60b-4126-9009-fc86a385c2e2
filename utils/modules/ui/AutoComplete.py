from discord import app_commands
import discord

from utils.modules.core.db.models import <PERSON><PERSON>

from sqlalchemy import select, Select

from sqlalchemy import select, or_

# Hubs : Moderator+
async def _hubm_autocomplete(interaction: discord.Interaction, current: str) -> list[app_commands.Choice[str]]:
    user_id = str(interaction.user.id)
    
    async with interaction.client.db.get_session() as session:
        stmt = select(Hub.name, Hub.id).where(or_(Hub.ownerId == user_id, Hub.moderators.any(id == user_id)))            
        result = await session.execute(stmt)
        hubs = result.all()

    choices = []
    for hub_name, hub_id in hubs:
        if current.lower() in hub_name.lower():
            choices.append(app_commands.Choice(name=hub_name, value=hub_id))
        
    return choices[:25]

def hubm_autocomplete(func): 
    return app_commands.autocomplete(hub=_hubm_autocomplete)(func)