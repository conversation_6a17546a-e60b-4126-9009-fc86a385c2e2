"""add some tables

Revision ID: e34e0549145a
Revises: 
Create Date: 2025-07-26 08:24:04.306457

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'e34e0549145a'
down_revision: Union[str, Sequence[str], None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('Blacklist',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('userId', sa.String(), nullable=False),
    sa.Column('moderatorId', sa.String(), nullable=False),
    sa.Column('reason', sa.String(), nullable=False),
    sa.Column('duration', sa.Integer(), nullable=True),
    sa.Column('expiresAt', sa.DateTime(), nullable=True),
    sa.Column('createdAt', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('updatedAt', sa.DateTime(), server_default=sa.text('now()'), nullable=True),
    sa.Column('type', sa.Enum('PERMANENT', 'TEMPORARY', name='BlacklistType'), nullable=False),
    sa.Column('status', sa.Enum('ACTIVE', 'EXPIRED', 'REVOKED', name='blackliststatus'), nullable=False),
    sa.ForeignKeyConstraint(['moderatorId'], ['User.id'], ),
    sa.ForeignKeyConstraint(['userId'], ['User.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('ix_blacklist_created_at', 'Blacklist', ['createdAt'], unique=False)
    op.create_index('ix_blacklist_expires_at', 'Blacklist', ['expiresAt'], unique=False)
    op.create_index('ix_blacklist_status', 'Blacklist', ['status'], unique=False)
    op.create_index('ix_blacklist_user_id', 'Blacklist', ['userId'], unique=False)
    op.create_table('ServerBlacklist',
    sa.Column('id', sa.String(), nullable=False),
    sa.Column('serverId', sa.String(), nullable=False),
    sa.Column('moderatorId', sa.String(), nullable=False),
    sa.Column('reason', sa.String(), nullable=False),
    sa.Column('duration', sa.Integer(), nullable=True),
    sa.Column('expiresAt', sa.DateTime(), nullable=True),
    sa.Column('createdAt', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.Column('updatedAt', sa.DateTime(), server_default=sa.text('now()'), nullable=False),
    sa.Column('type', sa.Enum('PERMANENT', 'TEMPORARY', name='BlacklistType'), nullable=False),
    sa.Column('status', sa.Enum('ACTIVE', 'EXPIRED', 'REVOKED', name='blackliststatus'), nullable=False),
    sa.ForeignKeyConstraint(['moderatorId'], ['User.id'], ),
    sa.ForeignKeyConstraint(['serverId'], ['ServerData.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('ix_server_blacklist_created_at', 'ServerBlacklist', ['createdAt'], unique=False)
    op.create_index('ix_server_blacklist_expires_at', 'ServerBlacklist', ['expiresAt'], unique=False)
    op.create_index('ix_server_blacklist_server_id', 'ServerBlacklist', ['serverId'], unique=False)
    op.create_index('ix_server_blacklist_status', 'ServerBlacklist', ['status'], unique=False)
    op.drop_index(op.f('ix_server_ban_created_at'), table_name='ServerBan')
    op.drop_index(op.f('ix_server_ban_expires_at'), table_name='ServerBan')
    op.drop_index(op.f('ix_server_ban_server_id'), table_name='ServerBan')
    op.drop_index(op.f('ix_server_ban_status'), table_name='ServerBan')
    op.drop_table('ServerBan')
    op.drop_index(op.f('ix_moderation_log_action'), table_name='ModerationLog')
    op.drop_index(op.f('ix_moderation_log_hub_id'), table_name='ModerationLog')
    op.drop_index(op.f('ix_moderation_log_moderator_id'), table_name='ModerationLog')
    op.drop_table('ModerationLog')
    op.drop_index(op.f('ix_ban_created_at'), table_name='Ban')
    op.drop_index(op.f('ix_ban_expires_at'), table_name='Ban')
    op.drop_index(op.f('ix_ban_status'), table_name='Ban')
    op.drop_index(op.f('ix_ban_user_id'), table_name='Ban')
    op.drop_table('Ban')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('Ban',
    sa.Column('id', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('userId', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('moderatorId', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('reason', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('duration', sa.INTEGER(), autoincrement=False, nullable=True),
    sa.Column('expiresAt', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.Column('createdAt', postgresql.TIMESTAMP(), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('updatedAt', postgresql.TIMESTAMP(), server_default=sa.text('now()'), autoincrement=False, nullable=True),
    sa.Column('type', postgresql.ENUM('PERMANENT', 'TEMPORARY', name='BanType'), autoincrement=False, nullable=False),
    sa.Column('status', postgresql.ENUM('ACTIVE', 'EXPIRED', 'REVOKED', name='banstatus'), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['moderatorId'], ['User.id'], name=op.f('Ban_moderatorId_fkey')),
    sa.ForeignKeyConstraint(['userId'], ['User.id'], name=op.f('Ban_userId_fkey')),
    sa.PrimaryKeyConstraint('id', name=op.f('Ban_pkey'))
    )
    op.create_index(op.f('ix_ban_user_id'), 'Ban', ['userId'], unique=False)
    op.create_index(op.f('ix_ban_status'), 'Ban', ['status'], unique=False)
    op.create_index(op.f('ix_ban_expires_at'), 'Ban', ['expiresAt'], unique=False)
    op.create_index(op.f('ix_ban_created_at'), 'Ban', ['createdAt'], unique=False)
    op.create_table('ModerationLog',
    sa.Column('id', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('hubId', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('moderatorId', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('action', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('targetId', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('reason', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('timestamp', postgresql.TIMESTAMP(), server_default=sa.text('now()'), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['hubId'], ['Hub.id'], name=op.f('ModerationLog_hubId_fkey'), ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['moderatorId'], ['User.id'], name=op.f('ModerationLog_moderatorId_fkey')),
    sa.PrimaryKeyConstraint('id', name=op.f('ModerationLog_pkey'))
    )
    op.create_index(op.f('ix_moderation_log_moderator_id'), 'ModerationLog', ['moderatorId'], unique=False)
    op.create_index(op.f('ix_moderation_log_hub_id'), 'ModerationLog', ['hubId'], unique=False)
    op.create_index(op.f('ix_moderation_log_action'), 'ModerationLog', ['action'], unique=False)
    op.create_table('ServerBan',
    sa.Column('id', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('serverId', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('moderatorId', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('reason', sa.VARCHAR(), autoincrement=False, nullable=False),
    sa.Column('duration', sa.INTEGER(), autoincrement=False, nullable=False),
    sa.Column('expiresAt', postgresql.TIMESTAMP(), autoincrement=False, nullable=True),
    sa.Column('createdAt', postgresql.TIMESTAMP(), server_default=sa.text('now()'), autoincrement=False, nullable=False),
    sa.Column('updatedAt', postgresql.TIMESTAMP(), server_default=sa.text('now()'), autoincrement=False, nullable=False),
    sa.Column('type', postgresql.ENUM('PERMANENT', 'TEMPORARY', name='BanType'), autoincrement=False, nullable=False),
    sa.Column('status', postgresql.ENUM('ACTIVE', 'EXPIRED', 'REVOKED', name='banstatus'), autoincrement=False, nullable=False),
    sa.ForeignKeyConstraint(['moderatorId'], ['User.id'], name=op.f('ServerBan_moderatorId_fkey')),
    sa.ForeignKeyConstraint(['serverId'], ['ServerData.id'], name=op.f('ServerBan_serverId_fkey')),
    sa.PrimaryKeyConstraint('id', name=op.f('ServerBan_pkey'))
    )
    op.create_index(op.f('ix_server_ban_status'), 'ServerBan', ['status'], unique=False)
    op.create_index(op.f('ix_server_ban_server_id'), 'ServerBan', ['serverId'], unique=False)
    op.create_index(op.f('ix_server_ban_expires_at'), 'ServerBan', ['expiresAt'], unique=False)
    op.create_index(op.f('ix_server_ban_created_at'), 'ServerBan', ['createdAt'], unique=False)
    op.drop_index('ix_server_blacklist_status', table_name='ServerBlacklist')
    op.drop_index('ix_server_blacklist_server_id', table_name='ServerBlacklist')
    op.drop_index('ix_server_blacklist_expires_at', table_name='ServerBlacklist')
    op.drop_index('ix_server_blacklist_created_at', table_name='ServerBlacklist')
    op.drop_table('ServerBlacklist')
    op.drop_index('ix_blacklist_user_id', table_name='Blacklist')
    op.drop_index('ix_blacklist_status', table_name='Blacklist')
    op.drop_index('ix_blacklist_expires_at', table_name='Blacklist')
    op.drop_index('ix_blacklist_created_at', table_name='Blacklist')
    op.drop_table('Blacklist')
    # ### end Alembic commands ###
