"""remove moderationCommand from Infractions

Revision ID: 5b643cd9095d
Revises: e34e0549145a
Create Date: 2025-07-26 10:36:15.847419

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '5b643cd9095d'
down_revision: Union[str, Sequence[str], None] = 'e34e0549145a'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('HubLogConfig', sa.Column('modLogsChannelId', sa.String(), nullable=False))
    op.add_column('HubLogConfig', sa.Column('modLogsRoleId', sa.String(), nullable=False))
    op.add_column('HubLogConfig', sa.Column('join_leavesChannelId', sa.String(), nullable=False))
    op.add_column('HubLogConfig', sa.Column('join_leavesRoleId', sa.String(), nullable=False))
    op.add_column('HubLogConfig', sa.Column('appealsChannelId', sa.String(), nullable=False))
    op.add_column('HubLogConfig', sa.Column('appealsRoleId', sa.String(), nullable=False))
    op.add_column('HubLogConfig', sa.Column('reportsChannelId', sa.String(), nullable=False))
    op.add_column('HubLogConfig', sa.Column('reportsRoleId', sa.String(), nullable=False))
    op.add_column('HubLogConfig', sa.Column('networkAlertsChannelId', sa.String(), nullable=False))
    op.add_column('HubLogConfig', sa.Column('networkAlertsRoleId', sa.String(), nullable=False))
    op.add_column('HubLogConfig', sa.Column('messageModerationChannelId', sa.String(), nullable=False))
    op.add_column('HubLogConfig', sa.Column('messageModerationRoleId', sa.String(), nullable=False))
    op.create_index('ix_hub_log_config_hub_id', 'HubLogConfig', ['hub_id'], unique=False)
    op.drop_column('HubLogConfig', 'reports_channel_id')
    op.drop_column('HubLogConfig', 'message_moderation_role_id')
    op.drop_column('HubLogConfig', 'mod_logs_channel_id')
    op.drop_column('HubLogConfig', 'network_alerts_role_id')
    op.drop_column('HubLogConfig', 'join_leaves_channel_id')
    op.drop_column('HubLogConfig', 'message_moderation_channel_id')
    op.drop_column('HubLogConfig', 'appeals_role_id')
    op.drop_column('HubLogConfig', 'join_leaves_role_id')
    op.drop_column('HubLogConfig', 'reports_role_id')
    op.drop_column('HubLogConfig', 'appeals_channel_id')
    op.drop_column('HubLogConfig', 'mod_logs_role_id')
    op.drop_column('HubLogConfig', 'network_alerts_channel_id')
    op.alter_column('Infraction', 'expiresAt',
               existing_type=postgresql.TIMESTAMP(),
               nullable=True)
    op.drop_column('Infraction', 'moderationCommand')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('Infraction', sa.Column('moderationCommand', sa.VARCHAR(), autoincrement=False, nullable=False))
    op.alter_column('Infraction', 'expiresAt',
               existing_type=postgresql.TIMESTAMP(),
               nullable=False)
    op.add_column('HubLogConfig', sa.Column('network_alerts_channel_id', sa.VARCHAR(), autoincrement=False, nullable=False))
    op.add_column('HubLogConfig', sa.Column('mod_logs_role_id', sa.VARCHAR(), autoincrement=False, nullable=False))
    op.add_column('HubLogConfig', sa.Column('appeals_channel_id', sa.VARCHAR(), autoincrement=False, nullable=False))
    op.add_column('HubLogConfig', sa.Column('reports_role_id', sa.VARCHAR(), autoincrement=False, nullable=False))
    op.add_column('HubLogConfig', sa.Column('join_leaves_role_id', sa.VARCHAR(), autoincrement=False, nullable=False))
    op.add_column('HubLogConfig', sa.Column('appeals_role_id', sa.VARCHAR(), autoincrement=False, nullable=False))
    op.add_column('HubLogConfig', sa.Column('message_moderation_channel_id', sa.VARCHAR(), autoincrement=False, nullable=False))
    op.add_column('HubLogConfig', sa.Column('join_leaves_channel_id', sa.VARCHAR(), autoincrement=False, nullable=False))
    op.add_column('HubLogConfig', sa.Column('network_alerts_role_id', sa.VARCHAR(), autoincrement=False, nullable=False))
    op.add_column('HubLogConfig', sa.Column('mod_logs_channel_id', sa.VARCHAR(), autoincrement=False, nullable=False))
    op.add_column('HubLogConfig', sa.Column('message_moderation_role_id', sa.VARCHAR(), autoincrement=False, nullable=False))
    op.add_column('HubLogConfig', sa.Column('reports_channel_id', sa.VARCHAR(), autoincrement=False, nullable=False))
    op.drop_index('ix_hub_log_config_hub_id', table_name='HubLogConfig')
    op.drop_column('HubLogConfig', 'messageModerationRoleId')
    op.drop_column('HubLogConfig', 'messageModerationChannelId')
    op.drop_column('HubLogConfig', 'networkAlertsRoleId')
    op.drop_column('HubLogConfig', 'networkAlertsChannelId')
    op.drop_column('HubLogConfig', 'reportsRoleId')
    op.drop_column('HubLogConfig', 'reportsChannelId')
    op.drop_column('HubLogConfig', 'appealsRoleId')
    op.drop_column('HubLogConfig', 'appealsChannelId')
    op.drop_column('HubLogConfig', 'join_leavesRoleId')
    op.drop_column('HubLogConfig', 'join_leavesChannelId')
    op.drop_column('HubLogConfig', 'modLogsRoleId')
    op.drop_column('HubLogConfig', 'modLogsChannelId')
    # ### end Alembic commands ###
