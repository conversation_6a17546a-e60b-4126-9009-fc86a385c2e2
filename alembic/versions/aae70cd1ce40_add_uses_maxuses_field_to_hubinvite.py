"""add uses & maxUses field to HubInvite

Revision ID: aae70cd1ce40
Revises: f18b2d8167b3
Create Date: 2025-07-27 21:03:59.654217

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'aae70cd1ce40'
down_revision: Union[str, Sequence[str], None] = 'f18b2d8167b3'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('HubInvite', sa.Column('maxUses', sa.Integer(), nullable=False))
    op.add_column('HubInvite', sa.Column('uses', sa.Integer(), nullable=False))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('HubInvite', 'uses')
    op.drop_column('HubInvite', 'maxUses')
    # ### end Alembic commands ###
