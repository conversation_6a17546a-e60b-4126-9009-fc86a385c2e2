"""remove logid from infraction

Revision ID: f18b2d8167b3
Revises: 5b643cd9095d
Create Date: 2025-07-26 10:48:31.585795

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'f18b2d8167b3'
down_revision: Union[str, Sequence[str], None] = '5b643cd9095d'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('Infraction', 'logId')
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('Infraction', sa.Column('logId', sa.VARCHAR(), autoincrement=False, nullable=False))
    # ### end Alembic commands ###
