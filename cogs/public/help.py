import discord 
from discord.ext import commands
from discord.ui import View, Select, select

from utils.constants import InterchatConstants
from utils.utils import check_user

from typing import TYPE_CHECKING
if TYPE_CHECKING:
    from main import Bot

from typing import TYPE_CHECKING
if TYPE_CHECKING:
    from main import Bot

class HelpView(View):
    def __init__(self, bot, user, commands):
        super().__init__(timeout=300)
        self.bot: Bot = bot
        self.user = user
        self.constants = InterchatConstants()
        self.commands = commands
        commands_list = []

        for category in commands:
            commands_list.append(discord.SelectOption(label=category, description=' ', value=category))
        
        self.help_select.options = commands_list

    @select(
        placeholder='Select a catagory',
        options=[],
        max_values=1,
        min_values=1
    )
    async def help_select(self, interaction: discord.Interaction, select: Select):
        embed = discord.Embed(title=select.values[0], description=self.commands[select.values[0]], color=self.constants.color())
        await interaction.response.edit_message(embed=embed)
    

class Help(commands.Cog):
    def __init__(self, bot):
        self.bot: Bot = bot
        self.constants = InterchatConstants()

    @commands.hybrid_command(name='help', description='📚 Explore InterChat commands with our new help system', extras={'category': 'General'})
    @check_user()
    async def help(self, ctx: commands.Context):
        """📚 Explore the InterChat commands available to you"""

        embed = discord.Embed(title='InterChat Commands', description='Explore our wide range of commands to help you, and your community connect with others.', color=self.constants.color())
        commands = {}

        command_ids = {f'{command.name} {child.name}': command.id for command in await self.bot.tree.fetch_commands() for child in command.options}
        command_ids.update({str(command): command.id for command in await self.bot.tree.fetch_commands()})

        for command in self.bot.walk_commands():
            category = command.extras.get('category')
            if category and category not in ('Group', 'Developer'):
                commands.setdefault(category, '')
                commands[category] += f'</{command}:{command_ids.get(str(command))}> \n> {command.description}\n\n'

        view = HelpView(self.bot, ctx.author, commands)
        await ctx.send(embed=embed, view=view, ephemeral=True)

async def setup(bot):
    await bot.add_cog(Help(bot))