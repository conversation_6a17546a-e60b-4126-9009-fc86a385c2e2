import discord
from discord.ext import commands

from utils.constants import InterchatConstants

class OnGuildJoin(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        self.constants = InterchatConstants()
    
    @commands.Cog.listener()
    async def on_guild_join(self, guild: discord.Guild):
        embed = discord.Embed(title='🎉 Welcome to InterChat!', description=f'Thank you for adding InterChat - Where now?\n{self.bot.emotes.dot} Create a community\n{self.bot.emotes.dot} Join a premade community\n{self.bot.emotes.dot} Build bridges\n{self.bot.emotes.dot} Learn new, exciting things\n{self.bot.emotes.dot} And so much more\n\n So what are you waiting for...? Use the /setup command and begin your journey with us.', color=self.constants.color())
        for channel in sorted(guild.text_channels, key=lambda x: x.position):
            everyone = channel.permissions_for(guild.default_role)
            bot = channel.permissions_for(guild.me)

            if everyone.send_messages and bot.send_messages:
                await channel.send(embed=embed)
                return

async def setup(bot):
    await bot.add_cog(OnGuildJoin(bot))