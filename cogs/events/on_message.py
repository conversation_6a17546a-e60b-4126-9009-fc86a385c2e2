from collections.abc import Sequence
from urllib.parse import urlparse
import aiohttp
import discord
from discord.ext import commands
from typing import TYPE_CHECKING, Optional
from sqlalchemy import select, insert
from sqlalchemy.dialects.postgresql import insert as pg_insert
from utils.modules.core.db.models import Connection, Hub, Message, Broadcast, User
from utils.constants import logger
import re
from datetime import datetime

if TYPE_CHECKING:
    from main import Bot
    from sqlalchemy.ext.asyncio import AsyncSession


class OnMessageCreate(commands.Cog):
    def __init__(self, bot):
        self.bot: "Bot" = bot

    def _is_image_url(self, url: str) -> bool:
        """Check if URL is an image (png, webp, jpg, jpeg)"""
        parsed_url = urlparse(url.lower())
        return parsed_url.path.endswith((".png", ".webp", ".jpg", ".jpeg"))

    def _is_gif_url(self, url: str) -> bool:
        """Check if URL is a GIF"""
        parsed_url = urlparse(url.lower())
        return parsed_url.path.endswith(".gif")

    def _is_tenor_gif(self, url: str) -> bool:
        """Check if URL is from Tenor"""
        return "tenor.com" in url.lower()

    def _process_content_with_attachments(self, message: discord.Message) -> str:
        """Process message content and add attachment/sticker URLs"""
        content = message.content

        # Process attachments
        for attachment in message.attachments:
            if self._is_image_url(attachment.url) or self._is_gif_url(attachment.url):
                # Add image/GIF with special format
                content += f"\n[⁥]({attachment.url})"

        # Process stickers
        for sticker in message.stickers:
            content += f"\n[⁥]({sticker.url})"

        # Check for image/GIF URLs in content
        url_pattern = r"https?://[^\s]+"
        urls = re.findall(url_pattern, content)

        for url in urls:
            if self._is_gif_url(url) and not self._is_tenor_gif(url):
                # Remove non-Tenor GIF URLs from content
                content = content.replace(
                    url, "[GIF blocked - only Tenor GIFs allowed]"
                )
            else:
                # Remove other URLs from content
                content = content.replace(url, "` URL removed `")

        return content

    async def _create_reply_embed(
        self, message: discord.Message, session: "AsyncSession"
    ) -> Optional[discord.Embed]:
        """Create reply embed if message is a reply"""
        if not message.reference or not message.reference.message_id:
            return None

        replied_message_id = str(message.reference.message_id)

        # First check if it's a broadcasted message
        stmt = (
            select(Broadcast, Message, User)
            .join(Message, Broadcast.messageId == Message.id)
            .join(User, Message.authorId == User.id)
            .where(Broadcast.id == replied_message_id)
        )
        result = await session.execute(stmt)
        row = result.first()

        if row:
            _, original_message, author = row
            return self._create_embed_from_message_and_author(original_message, author)

        # Check if it's an original message
        stmt = (
            select(Message, User)
            .join(User, Message.authorId == User.id)
            .where(Message.id == replied_message_id)
        )
        result = await session.execute(stmt)
        row = result.first()

        if row:
            original_message, author = row
            return self._create_embed_from_message_and_author(original_message, author)

        return None

    def _create_embed_from_message_and_author(self, message, author) -> discord.Embed:
        """Helper method to create embed from message and author"""
        # Truncate username to 30 characters
        username = author.name[:30] if author.name else "Unknown User"

        # Truncate content to 100 characters
        content = message.content[:100] + ("..." if len(message.content) > 100 else "")

        embed = discord.Embed(
            description=content,
            color=discord.Colour.random(),
        )
        embed.set_author(name=username, icon_url=author.image)
        return embed

    async def _upsert_user(
        self, message: discord.Message, session: "AsyncSession"
    ) -> None:
        """Upsert user with efficient database operations"""
        user_id = str(message.author.id)
        current_name = message.author.name
        current_avatar = message.author.display_avatar.url

        # Use PostgreSQL UPSERT instead of SQLAlchemy
        stmt = pg_insert(User).values(
            id=user_id,
            name=current_name,
            image=current_avatar,
            lastMessageAt=datetime.now(),
            updatedAt=datetime.now(),
        )

        # On conflict, update name, image, lastMessageAt, and updatedAt if they differ
        stmt = stmt.on_conflict_do_update(
            index_elements=["id"],
            set_={
                "name": stmt.excluded.name,
                "image": stmt.excluded.image,
                "lastMessageAt": stmt.excluded.lastMessageAt,
                "updatedAt": stmt.excluded.updatedAt,
            },
            where=(
                (User.name != stmt.excluded.name) | (User.image != stmt.excluded.image)
            ),
        )
        await session.execute(stmt)

    async def _store_message_and_broadcasts(
        self,
        message: discord.Message,
        hub: Hub,
        processed_content: str,
        broadcast_message_ids: list[tuple[str, str]],
        session: "AsyncSession",
    ) -> None:
        """Store original message and broadcast records in database"""

        # Extract image URL from processed content if present
        image_url = None
        if "[⁥](" in processed_content:
            # TODO: This only extracts first image URL, update schema to support multiple images
            import re

            match = re.search(r"\[⁥\]\(([^)]+)\)", processed_content)
            if match:
                image_url = match.group(1)

        # Store original message
        original_message = Message(
            id=str(message.id),
            hub_id=hub.id,
            content=processed_content,
            imageUrl=image_url,
            channelId=str(message.channel.id),
            guildId=str(message.guild.id) if message.guild else "",
            authorId=str(message.author.id),
            createdAt=message.created_at,
            referredMessageId=(
                str(message.reference.message_id)
                if message.reference and message.reference.message_id
                else None
            ),
            reactions=None,
        )

        session.add(original_message)
        await session.flush()

        # Store broadcast records
        for broadcast_id, channel_id in broadcast_message_ids:
            broadcast = Broadcast(
                id=broadcast_id,
                messageId=str(message.id),
                channelId=channel_id,
                createdAt=datetime.now(),
            )
            session.add(broadcast)

        await session.commit()

    @commands.Cog.listener()
    async def on_message(self, message: discord.Message):
        if message.author.bot or message.author.system:
            return

        # check db to see if message was sent in connected channel or not
        async with self.bot.db.get_session() as session:
            stmt = (
                select(Connection, Hub)
                .join(Hub, Connection.hubId == Hub.id)
                .where(
                    Connection.channelId == str(message.channel.id),
                    Connection.connected == True,
                )
            )

            result = await session.execute(stmt)
            row = result.first()

            if row is None:
                return

            connection, hub = row
            logger.info(f"Message from {message.author}: {message.content}")

            await self._upsert_user(message, session)

            processed_content = self._process_content_with_attachments(message)

            # Create reply embed only if this is a reply
            reply_embed = await self._create_reply_embed(message, session)

            # Get all other connections in the same hub (excluding current channel)
            other_connections_stmt = select(Connection).where(
                Connection.hubId == hub.id,
                Connection.channelId != str(message.channel.id),
                Connection.connected == True,
            )
            result = await session.execute(other_connections_stmt)
            other_connections = result.scalars().all()

            if not other_connections:
                logger.info(f"No other connections found for hub {hub.id}")
                return

            try:
                # Store original message and broadcast to other connections
                broadcast_message_ids = await self.broadcast_message(
                    message,
                    connection,
                    hub,
                    other_connections,
                    processed_content,
                    reply_embed,
                )

                # Store message and broadcast records in database
                await self._store_message_and_broadcasts(
                    message, hub, processed_content, broadcast_message_ids, session
                )

            except Exception as e:
                logger.error(f"Error broadcasting message: {e}")

    async def broadcast_message(
        self,
        message: discord.Message,
        connection: Connection,
        hub: Hub,
        other_connections: Sequence[Connection],
        processed_content: str,
        reply_embed: Optional[discord.Embed],
    ) -> list[tuple[str, str]]:
        """Broadcast message to other connections and return broadcast results"""
        broadcast_message_ids = []

        for other_conn in other_connections:
            async with aiohttp.ClientSession() as session:
                # Check if the webhook is already cached
                webhook = discord.Webhook.from_url(
                    url=other_conn.webhookUrl, session=session
                )

                # Prepare embeds list
                embeds = [reply_embed] if reply_embed else []

                # If the connection has a parentId, it means it's a thread
                if other_conn.parentId:
                    sent_message = await webhook.send(
                        processed_content,
                        username=f"{message.author.name} from {message.guild.name}",
                        avatar_url=message.author.display_avatar.url,
                        thread=discord.Object(id=other_conn.channelId),
                        embeds=embeds,
                        wait=True,
                    )
                else:
                    # Send the message using the webhook
                    sent_message = await webhook.send(
                        processed_content,
                        username=f"{message.author.name} from {message.guild.name}",
                        avatar_url=message.author.display_avatar.url,
                        embeds=embeds,
                        wait=True,
                    )

                # Store the broadcast message ID and channel ID
                if sent_message:
                    broadcast_message_ids.append(
                        (str(sent_message.id), other_conn.channelId)
                    )

        logger.info(
            f"Broadcasted message from {message.author} to {len(other_connections)} channels in hub {hub.id}"
        )

        return broadcast_message_ids


async def setup(bot):
    await bot.add_cog(OnMessageCreate(bot))
