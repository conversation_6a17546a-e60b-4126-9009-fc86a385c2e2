import discord
from discord.ext import commands
from discord import app_commands

class Report(commands.Cog):
    def __init__(self, bot):
        self.bot = bot
        
        self.report_message_menu = app_commands.ContextMenu(name='Report Message', callback=self.report_message)
        
        self.bot.tree.add_command(self.report_message_menu)
    
    async def cog_unload(self):
        self.bot.tree.remove_command(self.report_message_menu.name, type=discord.AppCommandType.message)
    
    async def report_message(self, interaction: discord.Interaction, message: discord.Message):
        if message.author == interaction.user:
            await interaction.response.send_message("You cannot report your own message!", ephemeral=True)
            return
        
        ... # TODO: pull context
        
async def setup(bot):
    await bot.add_cog(Report(bot))