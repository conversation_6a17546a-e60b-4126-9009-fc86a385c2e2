import discord
from discord.ext import commands
from discord.ui import TextInput, View, button, But<PERSON>, Select, select as discord_select
from sqlalchemy import select
from datetime import datetime, timedelta
from typing import TYPE_CHECKING, Optional

from utils.modules.core.db.models import (
    Infraction,
    InfractionType,
    InfractionStatus,
    Blacklist,
    ServerBlacklist,
    BlacklistType,
    BlacklistStatus,
    Hub,
)
from utils.modules.ui.CustomModal import CustomModal
from utils.modules.ui.ModerationViews import HubSelectionView, ModPanelView
from utils.modules.core.checks import interaction_check, is_interchat_staff
from utils.modules.core.moderation_utils import (
    get_user_moderated_hubs,
    fetch_original_message,
)
from utils.constants import InterchatConstants
from utils.utils import duration_to_datetime, parse_duration

if TYPE_CHECKING:
    from main import Bot


class Paginator(View):
    def __init__(self, bot, user, target_user, constants, locale, logs):
        super().__init__(timeout=300)

        self.bot = bot
        self.user = user
        self.target_user = target_user
        self.constants = constants
        self.locale = locale
        self.logs = logs
        self.current = 0
        self.message = None

        self.update_button_states()

    def fetch_message(self, message):
        self.message = message

    def update_button_states(self):
        if len(self.logs) == 0:
            self.jump_callback.disabled = True
            self.left_callback.disabled = True
            self.right_callback.disabled = True
            self.search_callback.disabled = True

        else:
            self.left_callback.disabled = self.current == 0
            self.right_callback.disabled = self.current == len(self.logs) - 1

        self.jump_callback.emoji = self.bot.emotes.hash_icon
        self.left_callback.emoji = self.bot.emotes.arrow_left
        self.right_callback.emoji = self.bot.emotes.arrow_right
        self.search_callback.emoji = self.bot.emotes.search_icon

    def get_embed(self) -> discord.Embed:
        if len(self.logs) == 0:
            return discord.Embed(
                title="Error!",
                description=f"{self.bot.emotes.x_icon} No logs found for this user.",
                color=discord.Color.red(),
            )

        logs = self.logs[self.current]
        embed = discord.Embed(
            title="Moderation History", description=" ", color=self.constants.color()
        )
        embed.set_author(
            name=f"@{self.target_user.name}",
            icon_url=self.target_user.display_avatar.url,
        )
        embed.add_field(
            name=logs.get("type", "N/A"), value=logs.get("date", "N/A"), inline=False
        )
        return embed

    @discord_select(
        placeholder="Select an action",
        options=[
            discord.SelectOption(label="Loading..", value="NONE")  # Placeholder option
        ],
        max_values=1,
        min_values=1,
    )
    async def on_submit(self, interaction: discord.Interaction, select: Select): ...

    @button(emoji="⚙️", style=discord.ButtonStyle.grey)
    async def jump_callback(self, interaction: discord.Interaction, button: Button):
        await interaction_check(interaction, self.user, interaction.user)
        ...  # Implement jump functionality here

    @button(emoji="◀️", style=discord.ButtonStyle.grey)
    async def left_callback(self, interaction: discord.Interaction, button: Button):
        await interaction_check(interaction, self.user, interaction.user)
        if self.current > 0:
            self.current -= 1
            self.update_button_states()
            embed = self.get_embed()
            await interaction.response.edit_message(embed=embed, view=self)

    @button(emoji="▶️", style=discord.ButtonStyle.grey)
    async def right_callback(self, interaction: discord.Interaction, button: Button):
        await interaction_check(interaction, self.user, interaction.user)
        if self.current < len(self.logs) - 1:
            self.current += 1
            self.update_button_states()
            embed = self.get_embed()
            await interaction.response.edit_message(embed=embed, view=self)

    @button(emoji="📖", style=discord.ButtonStyle.grey)
    async def search_callback(self, interaction: discord.Interaction, button: Button):
        await interaction_check(interaction, self.user, interaction.user)
        ...  # Implement search functionality here

class ModerationActionHandler:
    """Handler for executing moderation actions."""

    def __init__(
        self,
        bot: "Bot",
        moderator: discord.User | discord.Member,
        selected_hub: Hub,
        constants,
        locale: str,
    ):
        self.bot = bot
        self.moderator = moderator
        self.selected_hub = selected_hub
        self.constants = constants
        self.locale = locale

    async def handle_user_action(
        self,
        interaction: discord.Interaction,
        action: str,
        target_user: discord.User | discord.Member,
    ):
        """Handle moderation actions targeting a user."""
        if action == "warn":
            await self._create_warn_modal(interaction, target_user)
        elif action == "mute":
            await self._create_mute_modal(interaction, target_user)
        elif action == "ban":
            await self._create_ban_modal(interaction, target_user, None)
        elif action == "blacklist":
            await self._create_blacklist_modal(interaction, target_user, None)
        else:
            await interaction.response.send_message(
                f"{self.bot.emotes.x_icon} Unknown action: {action}",
                ephemeral=True,
            )

    async def handle_server_action(
        self,
        interaction: discord.Interaction,
        action: str,
        target_server: discord.Guild,
    ):
        """Handle moderation actions targeting a server."""
        if action in ["warn", "mute"]:
            await interaction.response.send_message(
                f"{self.bot.emotes.x_icon} Cannot {action} a server. Use ban or blacklist instead.",
                ephemeral=True,
            )
        elif action == "ban":
            await self._create_ban_modal(interaction, None, target_server)
        elif action == "blacklist":
            await self._create_blacklist_modal(interaction, None, target_server)
        else:
            await interaction.response.send_message(
                f"{self.bot.emotes.x_icon} Unknown action: {action}",
                ephemeral=True,
            )

    async def handle_delete_message(
        self, interaction: discord.Interaction, _: discord.Message
    ):
        """Handle message deletion across hubs."""
        # TODO: Implement message deletion across all connected hubs
        await interaction.response.send_message(
            f"{self.bot.emotes.x_icon} Message deletion across hubs is not yet implemented.",
            ephemeral=True,
        )

    async def _create_warn_modal(
        self,
        interaction: discord.Interaction,
        target_user: discord.User | discord.Member,
    ):
        """Create and show a modal for collecting warning reason."""
        modal = CustomModal(
            title="Warn User",
            options=[
                (
                    "reason",
                    TextInput(
                        label="Reason",
                        placeholder="Enter the reason for warning this user...",
                        style=discord.TextStyle.paragraph,
                        max_length=500,
                        required=True,
                    ),
                ),
            ],
            callback=lambda modal, inter: self._handle_warn_submission(
                modal, inter, target_user
            ),
        )
        await interaction.response.send_modal(modal)

    async def _create_mute_modal(
        self,
        interaction: discord.Interaction,
        target_user: discord.User | discord.Member,
    ):
        """Create and show a modal for collecting mute reason and duration."""
        modal = CustomModal(
            title="Mute User",
            options=[
                (
                    "reason",
                    TextInput(
                        label="Reason",
                        placeholder="Enter the reason for muting this user...",
                        style=discord.TextStyle.paragraph,
                        max_length=500,
                        required=True,
                    ),
                ),
                (
                    "duration",
                    TextInput(
                        label="Duration",
                        placeholder="Duration (e.g., 1d, 1w, 1m, 1y). Leave blank for permanent.",
                        max_length=10,
                        required=False,
                    ),
                ),
            ],
            callback=lambda modal, inter: self._handle_mute_submission(
                modal, inter, target_user
            ),
        )
        await interaction.response.send_modal(modal)

    async def _create_ban_modal(
        self,
        interaction: discord.Interaction,
        target_user: Optional[discord.User | discord.Member],
        target_server: discord.Guild,
    ):
        """Create and show a modal for collecting ban reason."""
        target_type = "User" if target_user else "Server"

        modal = CustomModal(
            title=f"Ban {target_type}",
            options=[
                (
                    "reason",
                    TextInput(
                        label="Reason",
                        placeholder=f"Enter the reason for banning this {target_type.lower()}...",
                        style=discord.TextStyle.paragraph,
                        max_length=500,
                        required=True,
                    ),
                ),
            ],
            callback=lambda modal, inter: self._handle_ban_submission(
                modal, inter, target_user, target_server
            ),
        )
        await interaction.response.send_modal(modal)

    async def _create_blacklist_modal(
        self,
        interaction: discord.Interaction,
        target_user: Optional[discord.User | discord.Member],
        target_server: discord.Guild,
    ):
        """Create and show a modal for collecting blacklist reason and duration."""
        target_type = "User" if target_user else "Server"

        modal = CustomModal(
            title=f"Blacklist {target_type}",
            options=[
                (
                    "reason",
                    TextInput(
                        label="Reason",
                        placeholder=f"Enter the reason for blacklisting this {target_type.lower()}...",
                        style=discord.TextStyle.paragraph,
                        max_length=500,
                        required=True,
                    ),
                ),
                (
                    "duration",
                    TextInput(
                        label="Duration",
                        placeholder="Duration (e.g., 1d, 1w, 1m, 1y). Leave blank for permanent.",
                        max_length=10,
                        required=False,
                    ),
                ),
            ],
            callback=lambda modal, inter: self._handle_blacklist_submission(
                modal, inter, target_user, target_server
            ),
        )
        await interaction.response.send_modal(modal)

    async def _handle_warn_submission(
        self,
        modal: CustomModal,
        interaction: discord.Interaction,
        target_user: discord.User | discord.Member,
    ):
        """Handle the submission of the warn modal."""
        reason = modal.saved_items["reason"].value

        async with self.bot.db.get_session() as session:
            infraction = Infraction(
                hubId=self.selected_hub.id,
                moderatorId=str(self.moderator.id),
                userId=str(target_user.id),
                serverId=None,
                serverName=None,
                reason=reason,
                type=InfractionType.WARNING,
                status=InfractionStatus.ACTIVE,
                notified=False,
                expiresAt=datetime.now() + timedelta(days=30),
                createdAt=datetime.now(),
                updatedAt=datetime.now(),
            )

            session.add(infraction)
            await session.commit()

            embed = discord.Embed(
                title=f"{self.bot.emotes.edit_icon} Warning Issued",
                description=f"Successfully warned {target_user.mention}",
                color=discord.Color.orange(),
            )
            embed.add_field(name="Reason", value=reason, inline=False)
            embed.add_field(name="Moderator", value=self.moderator.mention, inline=True)
            embed.add_field(name="Hub", value=self.selected_hub.name, inline=True)
            embed.set_footer(
                text=f"Expires: {infraction.expiresAt.strftime('%Y-%m-%d %H:%M:%S') if infraction.expiresAt else 'Never'}"
            )

            await interaction.followup.send(embed=embed, ephemeral=True)

    async def _handle_mute_submission(
        self,
        modal: CustomModal,
        interaction: discord.Interaction,
        target_user: discord.User | discord.Member,
    ):
        """Handle the submission of the mute modal."""
        reason = modal.saved_items["reason"].value
        duration_str = (
            modal.saved_items["duration"].value.strip()
            if modal.saved_items["duration"].value
            else ""
        )

        duration_ms = parse_duration(duration_str) if duration_str else None
        expires_at = duration_to_datetime(duration_ms)

        async with self.bot.db.get_session() as session:
            mute = Infraction(
                hubId=self.selected_hub.id,
                moderatorId=str(self.moderator.id),
                userId=str(target_user.id),
                serverId=None,
                serverName=None,
                reason=reason,
                expiresAt=expires_at,
                type=InfractionType.BLACKLIST,
                status=InfractionStatus.ACTIVE,
                notified=False,
                createdAt=datetime.now(),
                updatedAt=datetime.now(),
            )

            session.add(mute)
            await session.commit()

            embed = discord.Embed(
                title=f"{self.bot.emotes.clock_icon} User Muted",
                description=f"Successfully muted {target_user.mention}",
                color=discord.Color.orange(),
            )
            embed.add_field(name="Reason", value=reason, inline=False)
            embed.add_field(name="Moderator", value=self.moderator.mention, inline=True)
            embed.add_field(name="Hub", value=self.selected_hub.name, inline=True)

            if expires_at:
                embed.add_field(name="Duration", value=duration_str, inline=True)
                embed.set_footer(
                    text=f"Expires: {expires_at.strftime('%Y-%m-%d %H:%M:%S')}"
                )
            else:
                embed.add_field(name="Duration", value="Permanent", inline=True)

            await interaction.followup.send(embed=embed, ephemeral=True)

    async def _handle_ban_submission(
        self,
        modal: CustomModal,
        interaction: discord.Interaction,
        target_user: Optional[discord.User | discord.Member],
        target_server: discord.Guild,
    ):
        """Handle the submission of the ban modal."""
        reason = modal.saved_items["reason"].value

        async with self.bot.db.get_session() as session:
            ban_infraction = Infraction(
                hubId=self.selected_hub.id,
                moderatorId=str(self.moderator.id),
                userId=str(target_user.id) if target_user else None,
                serverId=str(target_server.id) if target_server else None,
                serverName=target_server.name if target_server else None,
                reason=reason,
                expiresAt=None,  # Bans are permanent
                type=InfractionType.BLACKLIST,
                status=InfractionStatus.ACTIVE,
                notified=False,
                createdAt=datetime.now(),
                updatedAt=datetime.now(),
            )
            session.add(ban_infraction)
            await session.commit()

            target_type = "user" if target_user else "server"
            if target_user:
                target_name = target_user.display_name
            elif target_server:
                target_name = target_server.name
            else:
                target_name = "Unknown"

            embed = discord.Embed(
                title=f"{self.bot.emotes.hammer_icon} {target_type.capitalize()} Banned",
                description=f"Successfully banned {target_type} **{target_name}**",
                color=discord.Color.red(),
            )
            embed.add_field(name="Reason", value=reason, inline=False)
            embed.add_field(name="Moderator", value=self.moderator.mention, inline=True)
            embed.add_field(name="Hub", value=self.selected_hub.name, inline=True)

            await interaction.followup.send(embed=embed, ephemeral=True)

    async def _handle_blacklist_submission(
        self,
        modal: CustomModal,
        interaction: discord.Interaction,
        target_user: Optional[discord.User | discord.Member],
        target_server: discord.Guild,
    ):
        """Handle the submission of the blacklist modal."""
        reason = modal.saved_items["reason"].value
        duration_str = (
            modal.saved_items["duration"].value.strip()
            if modal.saved_items["duration"].value
            else ""
        )

        duration_ms = parse_duration(duration_str) if duration_str else None
        expires_at = duration_to_datetime(duration_ms)
        ban_type = BlacklistType.TEMPORARY if duration_ms else BlacklistType.PERMANENT

        async with self.bot.db.get_session() as session:
            if target_user:
                # User blacklist
                blacklist = Blacklist(
                    userId=str(target_user.id),
                    moderatorId=str(self.moderator.id),
                    reason=reason,
                    duration=duration_ms,
                    expiresAt=expires_at,
                    type=ban_type,
                    status=BlacklistStatus.ACTIVE,
                )
                session.add(blacklist)
                target_name = target_user.display_name
                target_type = "User"
            elif target_server:
                # Server blacklist
                blacklist = ServerBlacklist(
                    serverId=str(target_server.id),
                    moderatorId=str(self.moderator.id),
                    reason=reason,
                    duration=duration_ms,
                    expiresAt=expires_at,
                    type=ban_type,
                    status=BlacklistStatus.ACTIVE,
                )
                session.add(blacklist)
                target_name = target_server.name
                target_type = "Server"
            else:
                await interaction.followup.send(
                    f"{self.bot.emotes.x_icon} No valid target provided.",
                    ephemeral=True,
                )
                return

            await session.commit()

            embed = discord.Embed(
                title=f"{self.bot.emotes.ban_icon} {target_type} Blacklisted",
                description=f"Successfully blacklisted {target_type.lower()} **{target_name}**",
                color=discord.Color.dark_red(),
            )
            embed.add_field(name="Reason", value=reason, inline=False)
            embed.add_field(name="Moderator", value=self.moderator.mention, inline=True)

            if expires_at:
                embed.add_field(name="Duration", value=duration_str, inline=True)
                embed.set_footer(
                    text=f"Expires: {expires_at.strftime('%Y-%m-%d %H:%M:%S')}"
                )
            else:
                embed.add_field(name="Duration", value="Permanent", inline=True)

            await interaction.followup.send(embed=embed, ephemeral=True)


class HubModeration(commands.Cog):
    def __init__(self, bot: "Bot"):
        self.bot = bot
        self.constants = InterchatConstants()
        self.locale = "en"

    @commands.hybrid_group()
    async def moderation(self, ctx: commands.Context[commands.Bot]):
        """Moderation commands for hub management."""
        pass

    @moderation.command(
        name="panel",
        description="Open the moderation panel for users, messages, or servers",
        extras={"category": "Hubs"},
    )
    async def panel(
        self,
        ctx: commands.Context[commands.Bot],
        *,
        user: Optional[discord.User] = None,
        message: discord.Message = None,
        server: discord.Guild = None,
    ):
        """
        Open the moderation panel.

        Args:
            user: User to moderate (optional)
            message: Message to moderate (optional)
            server: Server to moderate (optional)
        """
        await ctx.defer()

        # Get user's moderated hubs first
        user_hubs = await get_user_moderated_hubs(self.bot, str(ctx.author.id))
        if not user_hubs:
            return await ctx.send(
                f"{self.bot.emotes.x_icon} You don't have moderation permissions in any hubs.",
                ephemeral=True,
            )

        # Case 4: Message ID provided - fetch message details
        if message is not None:
            original_message = await fetch_original_message(self.bot, str(message.id))
            if not original_message:
                return await ctx.send(
                    f"{self.bot.emotes.x_icon} Original message not found in database.",
                    ephemeral=True,
                )

            # Extract message details
            try:
                target_user = await self.bot.fetch_user(int(original_message.authorId))
                target_server = await self.bot.fetch_guild(
                    int(original_message.guildId)
                )
            except (discord.NotFound, ValueError):
                return await ctx.send(
                    f"{self.bot.emotes.x_icon} Could not fetch message author or server.",
                    ephemeral=True,
                )

            # Get the hub from the message
            async with self.bot.db.get_session() as session:
                stmt = select(Hub).where(Hub.id == original_message.hub_id)
                selected_hub = (await session.execute(stmt)).scalar_one_or_none()

            if not selected_hub:
                return await ctx.send(
                    f"{self.bot.emotes.x_icon} Hub not found for this message.",
                    ephemeral=True,
                )

            # Show the mod panel directly
            view = ModPanelView(
                self.bot,
                ctx.author,
                target_user,
                target_server,
                message,
                selected_hub,
                self.constants,
                self.locale,
            )


            emotes = self.bot.emotes

            server_str = f"> **Server:** {target_server.name} (`{target_server.id}`)\n" if target_server else ""

            embed = discord.Embed(
                title="Moderation Panel",
                description=f"**{emotes.house_icon} Hub:** {selected_hub.name}\n\n"
                            f"**Context:**\n"
                            f"> **[Message Content]({message.jump_url})** {message.content.replace('`', '')[:50]}\n"
                            f"{server_str}"
                            f"> **Hub:** {selected_hub.name}\n\n",
                color=self.constants.color(),
            )

            user_info_str = (
                f"> **User:** {target_user.name} (`{target_user.id}`)\n"
                f"> **User Infractions:** {69}\n"
                f"> **Reputation:** {69}\n"
                if target_user
                else ""
            )
            server_info_str = (
                f"> **Server:** {target_server.name} (`{target_server.id}`)\n"
                f"> **Owner:** <@{target_server.owner_id}>\n"
                f"> **Server Infractions:** {69}\n"
                if target_server
                else ""
            )
            embed.add_field(
                name="Target Information",
                value=f"{user_info_str}{server_info_str}",
                inline=False,
            )

            if target_user:
                embed.set_author(name=target_user.name, icon_url=target_user.display_avatar.url)
            elif target_server:
                embed.set_author(name=target_server.name, icon_url=target_server.icon.url)

            await ctx.send(embed=embed, view=view)

        else:
            # Cases 1-3: No message provided, show hub selection first
            if not user and not server:
                return await ctx.send(
                    f"{self.bot.emotes.x_icon} You must provide either a user, server, or message parameter.",
                    ephemeral=True,
                )

            # Show hub selection view
            view = HubSelectionView(
                self.bot,
                ctx.author,
                user,
                server,
                None,  # No message
                user_hubs,
                self.constants,
                self.locale,
            )

            embed = discord.Embed(
                title="Hub Selection",
                description="Select a hub to perform moderation actions:",
                color=self.constants.color(),
            )

            if user:
                embed.add_field(
                    name="Target User",
                    value=f"{user.mention} (`{user.id}`)",
                    inline=True,
                )
            if server:
                embed.add_field(
                    name="Target Server",
                    value=f"**{server.name}** (`{server.id}`)",
                    inline=True,
                )

            embed.add_field(
                name="Hub",
                value="N/A - Select a hub from the dropdown below.",
                inline=False,
            )

            await ctx.send(embed=embed, view=view)


async def setup(bot):
    await bot.add_cog(HubModeration(bot))
