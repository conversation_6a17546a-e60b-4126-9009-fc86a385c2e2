from datetime import datetime
from discord import Thread
from unittest.mock import Mock
from zuid import <PERSON><PERSON><PERSON>

import discord
from discord.ext import commands
from discord.ui import View, select as discord_select, button, Button, TextInput

from sqlalchemy import select, Select
from utils.modules.core.checks import interaction_check
from utils.modules.core.db.models import Connection, Hub, HubInvite
from utils.modules.ui.AutoComplete import hubm_autocomplete
from utils.modules.errors.custom_discord import InvalidInput, InvalidInvite, WebhookError, NotConnected
from utils.constants import InterchatConstants
from utils.utils import check_user, get_or_create_webhook, get_webhook, get_and_cleanup_webhooks, duration_to_datetime, parse_duration
from utils.modules.ui.CustomModal import CustomModal

from typing import TYPE_CHECKING, Optional

if TYPE_CHECKING:
    from main import Bot

class ConfigurationView(View):
    def __init__(self, bot):
        super().__init__(timeout=300)
        self.bot = bot
    
    @discord_select(
        placeholder='Select an option',
        options=[
            None,
        ],
        max_values=1,
        min_values=1
    )
    async def on_submit(self, interaction: discord.Interaction, select: Select):
        ...

class InviteView(View):
    def __init__(self, bot, constants, user, hub):
        super().__init__(timeout=180)
        self.bot = bot
        self.constants = constants
        self.user = user
        self.hub = hub

    def setup_button(self):
        self.create_invite_callback.emoji = self.bot.emotes.link_icon

    @button(
        emoji='➕',
        label='Create',
        style=discord.ButtonStyle.grey
    )
    async def create_invite_callback(self, interaction: discord.Interaction, button: Button):
        await interaction_check(interaction, self.user, interaction.user)

        modal = CustomModal(
            'Input Required',
            [
                (
                    'customcode',
                    TextInput(
                        label='Custom Code',
                        placeholder='abc123',
                        required=False,
                        max_length=40
                    )
                ),
                (
                    'expire',
                    TextInput(
                        label='Expiry',
                        placeholder='1 week',
                        required=True
                    )
                )
            ]
        )
        try:
            await interaction.response.send_modal(modal)
            await modal.wait()

            generator = ZUID(prefix=f'{modal.customcode.value}_' if modal.customcode.value is not None else None, length=15)
            
            invite = HubInvite(
                code=generator(),
                expires=duration_to_datetime(parse_duration(modal.expire.value)),
                hubId=self.hub
            )

            async with self.bot.db.get_session() as session:
                session.add(invite)
                await session.commit()
        except Exception as e:
            print(e)

class Hubs(commands.Cog):
    def __init__(self, bot):
        self.bot: Bot = bot
        self.constants = InterchatConstants()

    @commands.hybrid_command(name="hubs", description="View InterChat hubs", extras={"category": "Hubs"})
    @check_user()
    async def hubs(self, ctx: commands.Context[commands.Bot]):
        async with self.bot.db.get_session() as session:
            hubs = (await session.execute(select(Hub))).scalars().all()
            if not hubs:
                await ctx.send(content="no hubs found")
                return

            content = ""
            for hub in hubs:
                content += f"{hub.name} ({hub.id}): {hub.shortDescription} | {hub.description}\n"
            await ctx.send(content=content)

    @commands.hybrid_group()
    async def hub(self, ctx: commands.Context[commands.Bot]):
        pass

    @hub.command(name="create", description="Create an InterChat hub", extras={"category": "Hubs"})
    @check_user()
    async def create_hub(
        self,
        ctx: commands.Context[commands.Bot],
        hub_name: str,
        short_description: str,
        description: str,
        icon_url: str,
    ):  # i'd do this interactivley tbh with a view
        async with self.bot.db.get_session() as session:
            hub = Hub(
                name=hub_name,
                shortDescription=short_description,
                description=description,
                ownerId=str(ctx.author.id),
                iconUrl=icon_url,
                rules=[],
                createdAt=datetime.now(),
                updatedAt=datetime.now(),
                lastActive=datetime.now(),
            )
            session.add(hub)
            await ctx.send(content="hub created")
            return

        await ctx.send(content="Failed to create hub. Please try again later.")

    @hub.command(name="configure", description="Configure your InterChat hub", extras={"category": "Hubs"})
    @check_user()
    async def configure(self, ctx: commands.Context[commands.Bot], hub):
        embed = discord.Embed(title='Hub Configuration', description='Edit the configuration of your InterChat hub for your community.', color=self.constants.color())
        view=None
        await ctx.send(embed=embed, view=view)

    @hub.command(name='delete', description='🗑️ Delete an InterChat hub', extras={'category': 'Hubs'}) # TODO: Disconnect hubs 
    @check_user()
    async def delete_hub(self, ctx: commands.Context[commands.Bot], hub: str):
        async with self.bot.db.get_session() as session:
            hub_obj = (await session.execute(select(Hub).where(Hub.name == hub))).scalar_one_or_none()
            if not hub_obj:
                await ctx.send(content="Hub not found")
                return

            if str(hub_obj.ownerId) != str(ctx.author.id):
                raise discord.Forbidden(Mock(403), "You are not the owner of this hub.")

            await session.delete(hub_obj)
            await session.commit()
            await ctx.send(content="Hub deleted successfully")
    
    @hub.command()
    #@commands.has_permissions(manage_channel=True)
    @check_user()
    async def join(self, ctx: commands.Context[commands.Bot], hub: str = None, invite: str = None, channel: Optional[discord.TextChannel | discord.Thread] = None):
        if hub is None and invite is None:
            raise InvalidInput()

        if not ctx.guild:
            raise commands.NoPrivateMessage()

        selected_channel = channel or ctx.channel

        result_invite = None
        result_hub = None

        if invite:
            async with self.bot.db.get_session() as session:
                stmt = select(HubInvite).where(HubInvite.code == invite)
                result_invite = (await session.execute(stmt)).scalar_one_or_none()

                if not result_invite:
                    raise InvalidInvite()
                
                if result_invite.expires <= datetime.now():
                    await session.delete(result_invite)
                    await session.commit()
                    raise InvalidInvite()
                
                stmt = select(Hub).where(Hub.id == result_invite.hubId)
                result_hub = (await session.execute(stmt)).scalar_one_or_none()
                await session.delete(result_invite)
                await session.commit()

        elif hub: 
            async with self.bot.db.get_session() as session:
                stmt = select(Hub).where((Hub.name == hub) & (Hub.private == False))
                result_hub = (await session.execute(stmt)).scalar_one_or_none()

                if not result_hub:
                    raise InvalidInput()

        if result_hub is None:
            raise InvalidInput()

        webhook = await get_or_create_webhook(self.bot, selected_channel)

        if not webhook:
            raise WebhookError()

        connection = Connection(
            hubId=result_hub.id, 
            channelId=str(selected_channel.id),
            webhookUrl=webhook.url,
            parentId=str(selected_channel.parent.id) if isinstance(selected_channel, discord.Thread) else None,
            invite=result_invite.code if result_invite else None, 
            serverId=str(ctx.guild.id),
            createdAt=datetime.now(), 
            lastActive=datetime.now(),
        )

        async with self.bot.db.get_session() as session:
            session.add(connection)
            await session.commit()

        embed = discord.Embed(title='Connected!', description=f'{self.bot.emotes.tick} Connected to {result_hub.name} - get chatting!', color=self.constants.color())
        embed.set_author(name=f'@{ctx.author.name}', icon_url=ctx.author.avatar.url)
        embed.set_footer(text=f'Connected Channel: {selected_channel}')
        await ctx.send(embed=embed)

    @hub.command()
    #@commands.has_permissions(manage_channel=True)
    @check_user()
    async def leave(self, ctx: commands.Context[commands.Bot]):
        async with self.bot.db.get_session() as session:
            stmt = select(Connection).where(Connection.channelId == str(ctx.channel.id))
            result = (await session.execute(stmt)).scalar_one_or_none()

            if not result:
                raise NotConnected()

            await session.delete(result)
            await session.commit()
            
        await get_and_cleanup_webhooks(self.bot, ctx.channel)

        embed = discord.Embed(title='Disconnected!', description=f'{self.bot.emotes.tick} Disconnected from hub!', color=self.constants.color())
        embed.set_author(name=f'@{ctx.author.name}', icon_url=ctx.author.avatar.url)
        embed.set_footer(text=f'Disconnected Channel: {ctx.channel}')
        await ctx.send(embed=embed)

    @hub.command()
    @hubm_autocomplete
    @check_user(True)
    async def invites(self, ctx: commands.Context[commands.Bot], hub: str):    
        async with self.bot.db.get_session() as session:
            stmt = select(HubInvite).where(HubInvite.hubId == hub)
            result = await session.execute(stmt)
            all_invites = result.scalars().all()
            
            expired_invites = []
            active_invites = []
            
            current_time = datetime.now() 
            
            for invite in all_invites:
                if invite.expires <= current_time:
                    expired_invites.append(invite)
                else:
                    active_invites.append(invite)
            
            if expired_invites:
                for expired_invite in expired_invites:
                    await session.delete(expired_invite)
                await session.commit()

        embed = discord.Embed(title='Active invites', description=' ', color=self.constants.color())
        
        if active_invites:
            for invite in active_invites:
                embed.add_field(name=f'{invite.code}', value=f'> **Expires:** <t:{int(invite.expires.timestamp())}:R>', inline=True)
        else:
            embed.description = f'{self.bot.emotes.x_icon} None found'
        
        view = InviteView(self.bot, self.constants, ctx.author, hub)
        view.setup_button()
        await ctx.send(embed=embed, view=view, ephemeral=True)

async def setup(bot: commands.Bot):
    await bot.add_cog(Hubs(bot))
